defmodule Drops.Operations.Extension do
  @moduledoc """
  Behaviour for Operations extensions.

  Extensions allow adding functionality to Operations modules based on configuration.
  For example, the Ecto extension adds changeset validation, persistence, and
  Phoenix.HTML.FormData protocol support when a repo is configured.

  ## Extension Interface

  Extensions must implement the following callbacks:

  - `enabled?/1` - Determines if the extension should be loaded based on options
  - `extend_using_macro/1` - Returns quoted code to inject into the `__using__` macro
  - `extend_operation_runtime/1` - Returns quoted code for runtime operation modules

  ## Example Extension

      defmodule MyExtension do
        @behaviour Drops.Operations.Extension

        @impl true
        def enabled?(opts) do
          Keyword.has_key?(opts, :my_option)
        end

        @impl true
        def extend_using_macro(opts) do
          quote do
            # Code to inject into the main __using__ macro
          end
        end

        @impl true
        def extend_operation_runtime(opts) do
          quote do
            # Code to inject into runtime operation modules
          end
        end
      end

  ## Parameters

  - `opts` - The options passed to the Operations module

  ## Returns

  Returns `true` if the extension should be loaded, `false` otherwise.
  """
  @callback enabled?(opts :: keyword()) :: boolean()

  defmacro __using__(_opts) do
    quote do
      import Drops.Operations.Extension, only: [steps: 1, operation: 1]
    end
  end

  @doc """
  Defines the steps that this extension provides.

  Extensions can use this macro to define their step functions, similar to
  how Operations modules define their steps. This allows the framework to
  properly track which functions are provided by extensions for filtering
  during parent module imports.

  ## Example

      defmodule MyExtension do
        use Drops.Operations.Extension

        steps do
          [:my_step, :another_step]
        end

        def extend_operation(opts) do
          quote do
            def my_step(context) do
              # implementation
            end

            def another_step(context) do
              # implementation
            end
          end
        end
      end

  ## Parameters

  - `block` - A block that returns a list of step function names (atoms)

  ## Returns

  Stores the steps in a module attribute and defines a `steps/0` function.
  """
  defmacro steps(do: block) do
    quote do
      @steps unquote(Macro.escape(block))

      def steps, do: @steps
    end
  end

  defmacro operation(do: block) do
    quote do
      @operation unquote(Macro.escape(block))

      def operation, do: @operation
    end
  end

  @doc """
  Allows extensions to modify the UnitOfWork for an operation.

  This is called after the UnitOfWork is created to allow extensions
  to override specific steps in the processing pipeline.

  ## Parameters

  - `uow` - The UnitOfWork to modify
  - `opts` - The options for the operation

  ## Returns

  Returns the modified UnitOfWork.
  """
  @callback extend_unit_of_work(
              uow :: Drops.Operations.UnitOfWork.t(),
              Module.t(),
              opts :: keyword()
            ) ::
              Drops.Operations.UnitOfWork.t()

  @doc """
  Returns default schema options for this extension.

  This callback allows extensions to provide default schema options
  that will be merged into opts.schema. For example, the Ecto extension
  can set cast: true by default.

  ## Parameters

  - `opts` - The current options for the operation

  ## Returns

  Returns a keyword list of schema options to be merged.
  """
  @callback schema_opts(opts :: keyword()) :: keyword()

  @optional_callbacks extend_unit_of_work: 3, schema_opts: 1

  @doc """
  Get enabled extensions based on the provided options and registered extensions.

  Extensions are enabled if they are registered and their enabled?(opts) callback returns true.

  ## Parameters

  - `registered_extensions` - List of registered extension modules
  - `opts` - The options to check against

  ## Returns

  Returns a list of extension modules that should be enabled.
  """
  def enabled_extensions(registered_extensions, opts) do
    registered_extensions
    |> Enum.filter(fn extension -> extension.enabled?(opts) end)
    |> Enum.uniq()
  end

  @doc """
  Generate extension code for runtime operation modules.

  ## Parameters

  - `registered_extensions` - List of registered extension modules
  - `opts` - The merged options for the operation

  ## Returns

  Returns a tuple of {quoted_code_list, function_names_list} where:
  - quoted_code_list: List of quoted code from all enabled extensions
  - function_names_list: List of {name, arity} tuples for functions defined by extensions
  """
  def extend_operation(registered_extensions, opts) do
    enabled_extensions(registered_extensions, opts)
    |> Enum.map(fn extension ->
      # extension_code =
      #   if function_exported?(extension, :operation, 0) do
      #     ast = extension.operation()

      #     quote(location: :keep) do
      #       unquote(ast)
      #     end
      #   else
      #     []
      #   end

      extension_code =
        if function_exported?(extension, :extend_operation, 1) do
          extension.extend_operation(opts)
        else
          []
        end

      extension_steps =
        if function_exported?(extension, :steps, 0) do
          ast = extension.steps()

          quote(location: :keep) do
            unquote(ast)
          end
        else
          []
        end

      function_names = extract_function_names(extension_code)
      step_function_names = extract_step_function_names(extension_steps)

      {[extension_code, extension_steps], function_names ++ step_function_names}
    end)
    |> Enum.unzip()
  end

  @doc """
  Extract function names from quoted AST.
  """
  def extract_function_names(ast) do
    {_ast, function_names} =
      Macro.prewalk(ast, [], fn
        {:def, _meta, [{name, _meta2, args} | _]} = node, acc when is_atom(name) ->
          arity = if is_list(args), do: length(args), else: 0
          {node, [{name, arity} | acc]}

        {:defp, _meta, [{name, _meta2, args} | _]} = node, acc when is_atom(name) ->
          arity = if is_list(args), do: length(args), else: 0
          {node, [{name, arity} | acc]}

        node, acc ->
          {node, acc}
      end)

    Enum.reverse(function_names)
  end

  @doc """
  Extract function names from steps AST.
  """
  def extract_step_function_names(steps_ast) do
    {_ast, function_names} =
      Macro.prewalk(steps_ast, [], fn
        {:def, _meta, [{name, _meta2, args} | _]} = node, acc when is_atom(name) ->
          arity = if is_list(args), do: length(args), else: 0
          {node, [{name, arity} | acc]}

        node, acc ->
          {node, acc}
      end)

    Enum.reverse(function_names)
  end

  @doc """
  Apply UnitOfWork extensions to modify the processing pipeline.

  ## Parameters

  - `uow` - The UnitOfWork to modify
  - `registered_extensions` - List of registered extension modules
  - `opts` - The options for the operation

  ## Returns

  Returns the modified UnitOfWork with extension overrides applied.
  """
  def extend_unit_of_work(uow, mod, registered_extensions, opts) do
    enabled_extensions(registered_extensions, opts)
    |> Enum.reduce(uow, fn extension, acc_uow ->
      if function_exported?(extension, :extend_unit_of_work, 3) do
        extension.extend_unit_of_work(acc_uow, mod, opts)
      else
        acc_uow
      end
    end)
  end

  @doc """
  Merge schema options from enabled extensions.

  This function collects schema options from all enabled extensions
  and merges them with the base schema options.

  ## Parameters

  - `registered_extensions` - List of registered extension modules
  - `opts` - The current options for the operation

  ## Returns

  Returns merged schema options.
  """
  def merge_schema_opts(registered_extensions, opts) do
    base_schema_opts = schema_opts(opts)

    extension_schema_opts =
      enabled_extensions(registered_extensions, opts)
      |> Enum.reduce([], fn extension, acc ->
        if function_exported?(extension, :schema_opts, 1) do
          extension_opts = extension.schema_opts(opts)
          Keyword.merge(acc, extension_opts)
        else
          acc
        end
      end)

    user_schema_opts = Keyword.get(opts, :schema, [])

    base_schema_opts
    |> Keyword.merge(extension_schema_opts)
    |> Keyword.merge(user_schema_opts)
  end

  @doc """
  Get base schema options based on operation type.

  ## Parameters

  - `opts` - The current options for the operation

  ## Returns

  Returns base schema options.
  """
  def schema_opts(opts) do
    case Keyword.get(opts, :type) do
      :form -> [atomize: true]
      _ -> []
    end
  end
end
