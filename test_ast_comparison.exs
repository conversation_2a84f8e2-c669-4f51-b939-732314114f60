defmodule TestExtension do
  use Drops.Operations.Extension

  # Using operation/0 macro
  operation do
    @behaviour Drops.Operations.Extensions.Ecto

    import Ecto.Changeset

    def ecto_schema, do: schema().meta[:source_schema]

    def repo, do: __opts__()[:repo]

    @impl true
    def validate_changeset(%{changeset: changeset}) do
      changeset
    end

    @impl true
    def get_struct(_context) do
      struct(ecto_schema())
    end
  end

  # Using extend_operation/1 function
  def extend_operation(_opts) do
    quote location: :keep do
      @behaviour Drops.Operations.Extensions.Ecto

      import Ecto.Changeset

      def ecto_schema, do: schema().meta[:source_schema]

      def repo, do: __opts__()[:repo]

      @impl true
      def validate_changeset(%{changeset: changeset}) do
        changeset
      end

      @impl true
      def get_struct(_context) do
        struct(ecto_schema())
      end
    end
  end

  def enabled?(_opts), do: true
end

# Test the AST output
operation_ast = TestExtension.operation()
extend_operation_ast = TestExtension.extend_operation([])

IO.puts("=== operation/0 AST ===")
IO.inspect(operation_ast, pretty: true, limit: :infinity)

IO.puts("\n=== extend_operation/1 AST ===")
IO.inspect(extend_operation_ast, pretty: true, limit: :infinity)

IO.puts("\n=== Are they equal? ===")
IO.inspect(operation_ast == extend_operation_ast)
